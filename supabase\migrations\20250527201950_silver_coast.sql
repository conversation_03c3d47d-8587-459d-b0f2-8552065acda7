/*
  # Initial Schema for <PERSON><PERSON>'s Burger

  1. New Tables
    - `menu_items` - Stores all menu items for both restaurant and cafe
    - `blog_posts` - Stores blog content
    - `content` - Stores website content sections

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated admin users
*/

-- Menu Items Table
CREATE TABLE IF NOT EXISTS menu_items (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text NOT NULL,
  price decimal(10, 2) NOT NULL,
  category text NOT NULL,
  menu_type text NOT NULL CHECK (menu_type IN ('restaurant', 'cafe')),
  image_url text,
  is_vegetarian boolean DEFAULT false,
  is_spicy boolean DEFAULT false,
  is_available boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
);

-- Blog Posts Table
CREATE TABLE IF NOT EXISTS blog_posts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  content text NOT NULL,
  excerpt text NOT NULL,
  image_url text,
  author text NOT NULL,
  category text NOT NULL,
  published boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- Content Sections Table
CREATE TABLE IF NOT EXISTS content (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  title text NOT NULL,
  content text NOT NULL,
  image_url text,
  display_order integer NOT NULL,
  section text NOT NULL,
  page text NOT NULL,
  created_at timestamptz DEFAULT now(),
  UNIQUE (section, page)
);

-- Enable Row Level Security
ALTER TABLE menu_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE content ENABLE ROW LEVEL SECURITY;

-- RLS Policies for menu_items
CREATE POLICY "Menu items are viewable by everyone"
  ON menu_items
  FOR SELECT
  USING (true);

CREATE POLICY "Menu items can be edited by authenticated users"
  ON menu_items
  FOR ALL
  TO authenticated
  USING (true);

-- RLS Policies for blog_posts
CREATE POLICY "Published blog posts are viewable by everyone"
  ON blog_posts
  FOR SELECT
  USING (published = true);

CREATE POLICY "All blog posts can be viewed by authenticated users"
  ON blog_posts
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Blog posts can be edited by authenticated users"
  ON blog_posts
  FOR ALL
  TO authenticated
  USING (true);

-- RLS Policies for content
CREATE POLICY "Content is viewable by everyone"
  ON content
  FOR SELECT
  USING (true);

CREATE POLICY "Content can be edited by authenticated users"
  ON content
  FOR ALL
  TO authenticated
  USING (true);

-- Insert default content
INSERT INTO content (name, title, content, image_url, display_order, section, page)
VALUES
  ('hero', 'Indulge in Burger Perfection', 'Handcrafted burgers made with premium ingredients for an unforgettable taste experience.', 'https://images.pexels.com/photos/1639562/pexels-photo-1639562.jpeg', 1, 'hero', 'home'),
  ('about', 'Our Story', 'Shebo''s Burger was founded in 2015 with a simple mission: create the most delicious, high-quality burgers using only the freshest ingredients. What started as a small food truck has grown into a beloved local restaurant, but our commitment to quality and flavor has never wavered.', 'https://images.pexels.com/photos/2983101/pexels-photo-2983101.jpeg', 2, 'about', 'home')
ON CONFLICT (section, page) DO NOTHING;