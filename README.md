# <PERSON><PERSON>'s Burger - Complete Restaurant Website

A modern, responsive website for <PERSON><PERSON>'s Burger restaurant built with React, TypeScript, Tailwind CSS, and Supabase.

## 🚀 Features

### Public Website
- **Homepage**: Hero section, about us, featured menu items, testimonials, contact & location
- **Menu Page**: Toggle between Restaurant and Café menus with filtering and search
- **Blog**: Category-based blog with rich content and social sharing
- **Responsive Design**: Mobile-first approach with beautiful animations
- **SEO Optimized**: Meta tags, structured data, and social media integration

### Admin Dashboard
- **Menu Management**: Add, edit, delete menu items with categories and dietary tags
- **Blog Management**: Create and manage blog posts with rich HTML content
- **Content Management**: Edit homepage sections and content
- **Sample Data Loader**: Populate database with demo content for testing

### Technical Features
- **Authentication**: Secure admin access with Supabase Auth
- **Database**: PostgreSQL with Row Level Security (RLS)
- **Image Optimization**: Lazy loading and performance optimization
- **Social Integration**: Facebook, Instagram, Twitter links and sharing
- **Google Maps**: Embedded location map
- **Accessibility**: WCAG compliant with proper contrast and navigation

## 🛠️ Tech Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Storage)
- **Animations**: Framer Motion
- **Forms**: React Hook Form
- **Date Handling**: date-fns
- **Icons**: Lucide React
- **Build Tool**: Vite

## 🎨 Design System

### Brand Colors
- **Primary**: #3B2619 (Dark Coffee)
- **Secondary**: #5A4031 (Mocha)
- **Background**: #F3EFE9 (Cream)
- **Accent/Borders**: #2A1A0D (Outline)

### Typography
- **Headings**: Playfair Display (serif)
- **Body**: Raleway (sans-serif)

## 📁 Project Structure

```
src/
├── components/
│   ├── admin/
│   │   ├── AdminLayout.tsx
│   │   └── SampleDataLoader.tsx
│   ├── layout/
│   │   ├── Navbar.tsx
│   │   └── Footer.tsx
│   ├── LazyImage.tsx
│   └── SEO.tsx
├── context/
│   └── AuthContext.tsx
├── lib/
│   └── supabaseClient.ts
├── pages/
│   ├── admin/
│   │   ├── Dashboard.tsx
│   │   ├── Menu.tsx
│   │   ├── Blog.tsx
│   │   └── Content.tsx
│   ├── Home.tsx
│   ├── Menu.tsx
│   ├── Blog.tsx
│   ├── BlogPost.tsx
│   ├── Login.tsx
│   └── NotFound.tsx
└── styles/
    └── index.css
```

## 🗄️ Database Schema

### Tables
- **menu_items**: Restaurant and café menu items
- **blog_posts**: Blog content with categories and publishing status
- **content**: Homepage and other content sections
- **users**: Managed by Supabase Auth

### Key Features
- Row Level Security (RLS) enabled
- Public read access for published content
- Admin-only write access for authenticated users

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd shebos-burger
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up Supabase Database**

   **Option A: Use existing demo database (recommended for testing)**
   - The project is already configured with a demo Supabase instance
   - Skip to step 5 to start the development server

   **Option B: Set up your own Supabase project**
   - Create a new project at [supabase.com](https://supabase.com)
   - Go to Settings > API to get your project URL and anon key
   - Update the `.env` file with your credentials:
     ```env
     VITE_SUPABASE_URL=your_supabase_url
     VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
     ```
   - In your Supabase dashboard, go to SQL Editor
   - Copy and run the contents of `database-setup.sql`

4. **Create an admin user**
   - Go to your Supabase dashboard > Authentication > Users
   - Click "Add user" and create an account
   - Use this account to access the admin dashboard

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Access the application**
   - Public website: `http://localhost:5173`
   - Admin dashboard: `http://localhost:5173/admin`

### Sample Data

To populate the database with sample content:
1. Log in to the admin dashboard
2. Go to the "Development Tools" section
3. Click "Load Sample Data"

## 🔐 Admin Access

To access the admin dashboard:
1. Create a user account in Supabase Auth
2. Navigate to `/login` and sign in
3. Access admin features at `/admin`

## 📱 Responsive Design

The website is fully responsive with breakpoints:
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## 🔍 SEO Features

- Dynamic meta tags for each page
- Open Graph and Twitter Card support
- Structured data (JSON-LD) for restaurant information
- Semantic HTML structure
- Image optimization with lazy loading
- Fast loading times with Vite

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Deploy to Vercel/Netlify
1. Connect your repository
2. Set environment variables
3. Deploy with build command: `npm run build`
4. Set output directory: `dist`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support or questions, please contact the development team or create an issue in the repository.

---

**Shebo's Burger** - Handcrafted burgers made with premium ingredients for an unforgettable taste experience.
