import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Calendar, User, Tag } from 'lucide-react';
import { format } from 'date-fns';
import { supabase, BlogPost } from '../lib/supabaseClient';

const Blog = () => {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeCategory, setActiveCategory] = useState('All');
  const [categories, setCategories] = useState<string[]>(['All']);

  useEffect(() => {
    const fetchBlogPosts = async () => {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('blog_posts')
        .select('*')
        .eq('published', true)
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching blog posts:', error);
      } else if (data) {
        setPosts(data);
        
        // Extract unique categories
        const uniqueCategories = ['All', ...new Set(data.map(post => post.category))];
        setCategories(uniqueCategories);
      }
      
      setLoading(false);
    };
    
    fetchBlogPosts();
  }, []);

  // If no posts from database, use default posts
  const defaultPosts: BlogPost[] = [
    {
      id: '1',
      title: 'The Secret to Our Juicy Burgers',
      content: `<p>At Shebo's Burger, we take pride in serving the juiciest, most flavorful burgers in town. But what's our secret? It all starts with quality ingredients.</p><p>We use only premium beef that's ground fresh daily. Our special blend of chuck and brisket provides the perfect fat content for a juicy, flavorful patty. We season our patties with a proprietary mix of spices that enhances the natural flavor of the beef without overwhelming it.</p><p>Another key factor is how we cook our burgers. We use a flat-top grill that sears the patties perfectly, locking in juices and creating a delicious crust. We always cook to order, ensuring each burger reaches your table at peak deliciousness.</p><p>Finally, we pay attention to the details. Our buns are baked fresh daily, and we toast them lightly for the perfect texture. Our toppings are always fresh and high-quality, from crisp lettuce to vine-ripened tomatoes.</p><p>Come taste the difference at Shebo's Burger!</p>`,
      excerpt: 'Discover what makes our burgers stand out from the competition - from ingredient selection to cooking techniques.',
      image_url: 'https://images.pexels.com/photos/3219547/pexels-photo-3219547.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      author: 'Chef Michael',
      category: 'Cooking Tips',
      published: true,
      created_at: '2023-06-15T12:00:00Z'
    },
    {
      id: '2',
      title: 'Upcoming Summer Special Menu',
      content: `<p>Summer is just around the corner, and we're excited to announce our new seasonal menu! We've created a selection of fresh, vibrant dishes that capture the essence of summer.</p><p>Our new Tropical Paradise Burger features a juicy beef patty topped with grilled pineapple, crispy bacon, and a zesty mango sauce. It's like a vacation in every bite! For seafood lovers, our Grilled Shrimp Burger with avocado and citrus slaw is a must-try.</p><p>We're also introducing some refreshing new sides. Our Watermelon Feta Salad is the perfect light accompaniment to any burger, and our Sweet Corn Fritters with chili-lime dip are already becoming a customer favorite.</p><p>To help you beat the heat, we've created new summer beverages including a Strawberry Basil Lemonade and a Coconut Cold Brew that are both refreshing and delicious.</p><p>Our summer menu launches on June 1st and will be available through August. Don't miss your chance to try these seasonal specialties!</p>`,
      excerpt: 'Get a sneak peek at our upcoming summer menu featuring seasonal ingredients and tropical flavors.',
      image_url: 'https://images.pexels.com/photos/1600711/pexels-photo-1600711.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      author: 'Sarah Williams',
      category: 'Menu Updates',
      published: true,
      created_at: '2023-05-20T14:30:00Z'
    },
    {
      id: '3',
      title: 'The History of Shebo\'s Burger',
      content: `<p>Shebo's Burger started from humble beginnings back in 2015. Founder James "Shebo" Williams had a passion for creating the perfect burger, experimenting with recipes in his home kitchen for years before deciding to share his creations with the world.</p><p>What began as a small food truck quickly gained a loyal following. Customers would line up for hours just to taste Shebo's unique burgers. The signature Shebo Sauce, a secret recipe known only to James and his head chef, became legendary in the local food scene.</p><p>After two successful years as a food truck, we opened our first brick-and-mortar location downtown. The restaurant's rustic-modern design, with reclaimed wood tables and industrial lighting, created the perfect atmosphere for enjoying our gourmet burgers.</p><p>Over the years, we've expanded our menu beyond burgers to include a variety of dishes, but we've never lost sight of our roots. Every item on our menu is crafted with the same care and attention to detail that went into that very first burger.</p><p>Today, Shebo's Burger is a beloved local institution, and we're proud to continue the tradition of serving high-quality, delicious food to our community.</p>`,
      excerpt: 'Learn about how Shebo\'s Burger grew from a small food truck to the beloved restaurant it is today.',
      image_url: 'https://images.pexels.com/photos/2983101/pexels-photo-2983101.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      author: 'James Williams',
      category: 'Our Story',
      published: true,
      created_at: '2023-04-10T09:15:00Z'
    }
  ];

  // If no posts from database, use default posts
  useEffect(() => {
    if (!loading && posts.length === 0) {
      setPosts(defaultPosts);
      
      // Extract unique categories
      const uniqueCategories = ['All', ...new Set(defaultPosts.map(post => post.category))];
      setCategories(uniqueCategories);
    }
  }, [loading, posts.length]);

  // Filter posts by category
  const filteredPosts = activeCategory === 'All' 
    ? posts 
    : posts.filter(post => post.category === activeCategory);

  return (
    <div className="pt-16">
      {/* Blog Header */}
      <section 
        className="relative py-24 bg-cover bg-center"
        style={{ 
          backgroundImage: `linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)), url(https://images.pexels.com/photos/1855214/pexels-photo-1855214.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2)` 
        }}
      >
        <div className="container-custom text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-5xl font-serif font-bold mb-4 text-white">Our Blog</h1>
            <p className="text-xl max-w-2xl mx-auto">
              Stay updated with the latest news, recipes, and stories from Shebo's Burger
            </p>
          </motion.div>
        </div>
      </section>

      {/* Blog Categories */}
      <section className="py-8 bg-cream">
        <div className="container-custom">
          <div className="overflow-x-auto">
            <div className="flex space-x-2 min-w-max">
              {categories.map((category) => (
                <button
                  key={category}
                  className={`px-6 py-2 rounded-md text-lg font-medium transition-colors ${
                    activeCategory === category 
                      ? 'bg-coffee text-white' 
                      : 'bg-white text-coffee border border-coffee hover:bg-coffee/10'
                  }`}
                  onClick={() => setActiveCategory(category)}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Blog Posts */}
      <section className="py-12">
        <div className="container-custom">
          {loading ? (
            <div className="text-center py-12">
              <p className="text-xl">Loading blog posts...</p>
            </div>
          ) : (
            <>
              {filteredPosts.length > 0 ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {filteredPosts.map((post, index) => (
                    <BlogPostCard 
                      key={post.id} 
                      post={post} 
                      featured={index === 0 && activeCategory === 'All'} 
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-xl">No blog posts found in this category.</p>
                </div>
              )}
            </>
          )}
        </div>
      </section>
    </div>
  );
};

// Blog Post Card Component
const BlogPostCard = ({ 
  post, 
  featured = false 
}: { 
  post: BlogPost; 
  featured?: boolean 
}) => {
  const formattedDate = format(new Date(post.created_at), 'MMMM d, yyyy');
  
  return (
    <motion.article 
      className={`blog-card ${featured ? 'lg:col-span-2' : ''}`}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.4 }}
    >
      <div className={`grid ${featured ? 'lg:grid-cols-2' : 'grid-cols-1'} gap-6`}>
        <div className={`${featured ? 'h-full' : 'h-64'} overflow-hidden`}>
          <img 
            src={post.image_url || 'https://images.pexels.com/photos/1855214/pexels-photo-1855214.jpeg'} 
            alt={post.title} 
            className="w-full h-full object-cover"
          />
        </div>
        
        <div className="p-6">
          <div className="flex flex-wrap gap-3 mb-3">
            <span className="flex items-center text-sm text-gray-600">
              <Calendar className="h-4 w-4 mr-1" />
              {formattedDate}
            </span>
            <span className="flex items-center text-sm text-gray-600">
              <User className="h-4 w-4 mr-1" />
              {post.author}
            </span>
            <span className="flex items-center text-sm text-gray-600">
              <Tag className="h-4 w-4 mr-1" />
              {post.category}
            </span>
          </div>
          
          <h2 className={`font-bold mb-3 ${featured ? 'text-3xl' : 'text-2xl'}`}>
            {post.title}
          </h2>
          <p className="text-gray-600 mb-4">
            {post.excerpt}
          </p>
          <Link 
            to={`/blog/${post.id}`} 
            className="inline-block text-coffee font-semibold hover:underline"
          >
            Read More
          </Link>
        </div>
      </div>
    </motion.article>
  );
};

export default Blog;