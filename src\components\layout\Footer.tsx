import { Link } from 'react-router-dom';
import { Facebook, Instagram, Twitter, MapPin, Phone, Mail } from 'lucide-react';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="bg-coffee text-cream">
      <div className="container-custom py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* About */}
          <div>
            <h4 className="text-xl font-serif font-bold mb-4 text-cream">About Us</h4>
            <p className="mb-4">
              <PERSON><PERSON>'s Burger offers an unforgettable dining experience with our handcrafted burgers
              made from the freshest ingredients. Come taste the difference!
            </p>
            <div className="flex space-x-4">
              <SocialLink href="#" icon={<Facebook className="h-5 w-5" />} label="Facebook" />
              <SocialLink href="#" icon={<Instagram className="h-5 w-5" />} label="Instagram" />
              <SocialLink href="#" icon={<Twitter className="h-5 w-5" />} label="Twitter" />
            </div>
          </div>
          
          {/* Quick Links */}
          <div>
            <h4 className="text-xl font-serif font-bold mb-4 text-cream">Quick Links</h4>
            <ul className="space-y-2">
              <FooterLink to="/" label="Home" />
              <FooterLink to="/menu" label="Menu" />
              <FooterLink to="/blog" label="Blog" />
              <FooterLink to="/login" label="Admin Login" />
            </ul>
          </div>
          
          {/* Opening Hours */}
          <div>
            <h4 className="text-xl font-serif font-bold mb-4 text-cream">Opening Hours</h4>
            <ul className="space-y-2">
              <li className="flex justify-between">
                <span>Monday - Friday</span>
                <span>8:00 AM - 10:00 PM</span>
              </li>
              <li className="flex justify-between">
                <span>Saturday</span>
                <span>9:00 AM - 11:00 PM</span>
              </li>
              <li className="flex justify-between">
                <span>Sunday</span>
                <span>9:00 AM - 9:00 PM</span>
              </li>
            </ul>
          </div>
          
          {/* Contact */}
          <div>
            <h4 className="text-xl font-serif font-bold mb-4 text-cream">Contact Us</h4>
            <ul className="space-y-3">
              <li className="flex items-start">
                <MapPin className="h-5 w-5 mr-2 mt-1 flex-shrink-0" />
                <span>123 Burger Street, Foodville, CA 90210</span>
              </li>
              <li className="flex items-center">
                <Phone className="h-5 w-5 mr-2 flex-shrink-0" />
                <span>(*************</span>
              </li>
              <li className="flex items-center">
                <Mail className="h-5 w-5 mr-2 flex-shrink-0" />
                <span><EMAIL></span>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-cream/20 mt-8 pt-6 flex flex-col md:flex-row justify-between items-center">
          <p>&copy; {currentYear} Shebo's Burger. All Rights Reserved.</p>
          <div className="mt-4 md:mt-0">
            <Link to="#" className="text-cream/80 hover:text-cream mr-4">
              Privacy Policy
            </Link>
            <Link to="#" className="text-cream/80 hover:text-cream">
              Terms of Service
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

// Footer Link Component
const FooterLink = ({ to, label }: { to: string; label: string }) => (
  <li>
    <Link to={to} className="text-cream/80 hover:text-cream transition-colors">
      {label}
    </Link>
  </li>
);

// Social Link Component
const SocialLink = ({ 
  href, 
  icon, 
  label 
}: { 
  href: string; 
  icon: React.ReactNode; 
  label: string 
}) => (
  <a 
    href={href} 
    className="bg-mocha hover:bg-cream hover:text-coffee rounded-full p-2 transition-colors"
    aria-label={label}
  >
    {icon}
  </a>
);

export default Footer;