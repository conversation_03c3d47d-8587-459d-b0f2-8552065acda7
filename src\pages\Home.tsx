import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ChevronRight, Star } from 'lucide-react';
import { supabase, MenuItem, ContentSection } from '../lib/supabaseClient';

const Home = () => {
  const [featuredItems, setFeaturedItems] = useState<MenuItem[]>([]);
  const [content, setContent] = useState<Record<string, ContentSection>>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      
      // Fetch content sections
      const { data: contentData, error: contentError } = await supabase
        .from('content')
        .select('*')
        .eq('page', 'home')
        .order('order');
      
      if (contentError) {
        console.error('Error fetching content:', contentError);
      } else if (contentData) {
        // Convert to record with section as key
        const contentMap: Record<string, ContentSection> = {};
        contentData.forEach((item: ContentSection) => {
          contentMap[item.section] = item;
        });
        setContent(contentMap);
      }
      
      // Fetch featured menu items
      const { data: menuData, error: menuError } = await supabase
        .from('menu_items')
        .select('*')
        .eq('is_available', true)
        .limit(4);
      
      if (menuError) {
        console.error('Error fetching menu items:', menuError);
      } else {
        setFeaturedItems(menuData || []);
      }
      
      setLoading(false);
    };
    
    fetchData();
  }, []);

  // If no content is loaded from database, use default content
  const heroContent = content.hero || {
    title: "Indulge in Burger Perfection",
    content: "Handcrafted burgers made with premium ingredients for an unforgettable taste experience.",
    image_url: "https://images.pexels.com/photos/1639562/pexels-photo-1639562.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"
  };
  
  const aboutContent = content.about || {
    title: "Our Story",
    content: "Shebo's Burger was founded in 2015 with a simple mission: create the most delicious, high-quality burgers using only the freshest ingredients. What started as a small food truck has grown into a beloved local restaurant, but our commitment to quality and flavor has never wavered.",
    image_url: "https://images.pexels.com/photos/2983101/pexels-photo-2983101.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"
  };

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section 
        className="relative h-[90vh] flex items-center justify-center bg-cover bg-center" 
        style={{ 
          backgroundImage: `linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url(${heroContent.image_url})` 
        }}
      >
        <div className="container-custom text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-6xl font-serif font-bold mb-4 text-white">
              {heroContent.title}
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-2xl mx-auto">
              {heroContent.content}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/menu" className="btn bg-coffee hover:bg-coffee/90 text-cream">
                View Our Menu
              </Link>
              <a href="#about" className="btn bg-transparent border-2 border-white hover:bg-white/20">
                Our Story
              </a>
            </div>
          </motion.div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="section-padding bg-cream">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl font-serif font-bold mb-6">{aboutContent.title}</h2>
              <p className="text-lg mb-8">{aboutContent.content}</p>
              <div className="flex gap-4">
                <Link to="/menu" className="btn btn-primary">
                  Explore Menu
                </Link>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="rounded-lg overflow-hidden shadow-xl"
            >
              <img 
                src={aboutContent.image_url} 
                alt="About Shebo's Burger" 
                className="w-full h-[400px] object-cover"
              />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Featured Menu Items */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-serif font-bold mb-4">Customer Favorites</h2>
            <p className="text-lg max-w-2xl mx-auto">
              Discover our most popular dishes loved by our customers
            </p>
          </div>
          
          {loading ? (
            <div className="text-center py-12">Loading featured items...</div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {featuredItems.length > 0 ? (
                  featuredItems.map((item) => (
                    <FeaturedMenuItem key={item.id} item={item} />
                  ))
                ) : (
                  // Fallback items if no data from database
                  <>
                    <FeaturedMenuItem 
                      item={{
                        id: '1',
                        name: 'Classic Cheeseburger',
                        description: 'Juicy beef patty with melted cheddar, lettuce, tomato, and special sauce',
                        price: 12.99,
                        category: 'Burgers',
                        menu_type: 'restaurant',
                        image_url: 'https://images.pexels.com/photos/1633578/pexels-photo-1633578.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
                        is_available: true,
                        created_at: ''
                      }} 
                    />
                    <FeaturedMenuItem 
                      item={{
                        id: '2',
                        name: 'Truffle Fries',
                        description: 'Crispy fries tossed with truffle oil, parmesan cheese, and fresh herbs',
                        price: 8.99,
                        category: 'Sides',
                        menu_type: 'restaurant',
                        image_url: 'https://images.pexels.com/photos/1893555/pexels-photo-1893555.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
                        is_available: true,
                        created_at: ''
                      }} 
                    />
                    <FeaturedMenuItem 
                      item={{
                        id: '3',
                        name: 'BBQ Bacon Burger',
                        description: 'Smoky beef patty with crispy bacon, cheddar, and tangy BBQ sauce',
                        price: 14.99,
                        category: 'Burgers',
                        menu_type: 'restaurant',
                        image_url: 'https://images.pexels.com/photos/3219547/pexels-photo-3219547.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
                        is_available: true,
                        created_at: ''
                      }} 
                    />
                    <FeaturedMenuItem 
                      item={{
                        id: '4',
                        name: 'Oreo Milkshake',
                        description: 'Creamy vanilla milkshake blended with Oreo cookies and topped with whipped cream',
                        price: 6.99,
                        category: 'Drinks',
                        menu_type: 'cafe',
                        image_url: 'https://images.pexels.com/photos/3727250/pexels-photo-3727250.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
                        is_available: true,
                        created_at: ''
                      }} 
                    />
                  </>
                )}
              </div>
              
              <div className="text-center mt-12">
                <Link to="/menu" className="btn btn-primary inline-flex items-center">
                  See Full Menu
                  <ChevronRight className="ml-2 h-5 w-5" />
                </Link>
              </div>
            </>
          )}
        </div>
      </section>

      {/* Testimonials */}
      <section className="section-padding bg-mocha text-cream">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-serif font-bold mb-4 text-cream">What Our Customers Say</h2>
            <p className="text-lg max-w-2xl mx-auto">
              Don't just take our word for it - hear from our satisfied customers
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <TestimonialCard 
              name="Sarah L."
              text="The Classic Cheeseburger is to die for! Juicy, flavorful, and the perfect portion size. Will definitely be coming back!"
              rating={5}
            />
            <TestimonialCard 
              name="Michael T."
              text="Best burger joint in town! The truffle fries are a must-try side. Great atmosphere and friendly staff too."
              rating={5}
            />
            <TestimonialCard 
              name="Jessica R."
              text="I'm obsessed with their milkshakes! The perfect treat after a long day. And their vegetarian options are actually delicious!"
              rating={4}
            />
          </div>
        </div>
      </section>

      {/* Location & Contact */}
      <section className="section-padding bg-cream">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <h2 className="text-3xl font-serif font-bold mb-6">Find Us</h2>
              <p className="text-lg mb-6">
                We're conveniently located in the heart of downtown. Come visit us today!
              </p>
              
              <div className="space-y-4 mb-8">
                <div className="flex items-start">
                  <div className="bg-coffee text-cream p-2 rounded-md mr-4">
                    <MapPin className="h-6 w-6" />
                  </div>
                  <div>
                    <h4 className="font-bold">Address</h4>
                    <p>123 Burger Street, Foodville, CA 90210</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-coffee text-cream p-2 rounded-md mr-4">
                    <Phone className="h-6 w-6" />
                  </div>
                  <div>
                    <h4 className="font-bold">Phone</h4>
                    <p>(*************</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-coffee text-cream p-2 rounded-md mr-4">
                    <Mail className="h-6 w-6" />
                  </div>
                  <div>
                    <h4 className="font-bold">Email</h4>
                    <p><EMAIL></p>
                  </div>
                </div>
              </div>
              
              <a 
                href="https://maps.google.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="btn btn-primary"
              >
                Get Directions
              </a>
            </div>
            
            <div className="rounded-lg overflow-hidden h-[400px] shadow-lg">
              {/* This would be a map in a real application */}
              <div className="w-full h-full bg-gray-300 flex items-center justify-center">
                <p className="text-xl font-bold text-gray-600">Google Maps Integration</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

// Featured Menu Item Component
const FeaturedMenuItem = ({ item }: { item: MenuItem }) => (
  <motion.div 
    className="menu-item group"
    whileHover={{ y: -5 }}
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true }}
    transition={{ duration: 0.4 }}
  >
    <div className="h-48 overflow-hidden">
      <img 
        src={item.image_url || 'https://images.pexels.com/photos/1633578/pexels-photo-1633578.jpeg'} 
        alt={item.name} 
        className="w-full h-full object-cover transition-transform group-hover:scale-105"
      />
    </div>
    <div className="p-4">
      <div className="flex justify-between items-start">
        <h3 className="font-bold text-xl">{item.name}</h3>
        <span className="text-lg font-bold text-coffee">${item.price.toFixed(2)}</span>
      </div>
      <p className="text-gray-600 mt-2 line-clamp-2">{item.description}</p>
    </div>
  </motion.div>
);

// Testimonial Card Component
const TestimonialCard = ({ 
  name, 
  text, 
  rating 
}: { 
  name: string; 
  text: string; 
  rating: number 
}) => (
  <motion.div 
    className="bg-white text-coffee p-6 rounded-lg shadow-md"
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true }}
    transition={{ duration: 0.4 }}
  >
    <div className="flex mb-4">
      {[...Array(5)].map((_, i) => (
        <Star 
          key={i} 
          className={`h-5 w-5 ${i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} 
        />
      ))}
    </div>
    <p className="mb-4 italic">"{text}"</p>
    <p className="font-bold">{name}</p>
  </motion.div>
);

export default Home;