import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Search, Filter } from 'lucide-react';
import { supabase, MenuItem } from '../lib/supabaseClient';
import { useLanguage } from '../contexts/LanguageContext';
import SEO from '../components/SEO';

type MenuType = 'cafe' | 'restaurant';
type Category = string;

const Menu = () => {
  const { language, t } = useLanguage();
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeType, setActiveType] = useState<MenuType>('restaurant');
  const [activeCategory, setActiveCategory] = useState<Category>('All');
  const [categories, setCategories] = useState<Category[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    vegetarian: false,
    spicy: false,
  });

  useEffect(() => {
    const fetchMenuItems = async () => {
      setLoading(true);
      const { data, error } = await supabase
        .from('menu_items')
        .select('*')
        .eq('is_available', true)
        .order('display_order', { ascending: true });

      if (error) {
        console.error('Error fetching menu items:', error);
      } else if (data) {
        setMenuItems(data);

        // Extract unique categories
        const uniqueCategories = ['All', ...new Set(data.map(item => item.category))];
        setCategories(uniqueCategories);
      }

      setLoading(false);
    };

    fetchMenuItems();
  }, []);

  // Update filtered items when filters change
  useEffect(() => {
    let result = menuItems;

    // Filter by menu type
    result = result.filter(item => item.menu_type === activeType);

    // Filter by category if not "All"
    if (activeCategory !== 'All') {
      result = result.filter(item => item.category === activeCategory);
    }

    // Apply vegetarian filter
    if (filters.vegetarian) {
      result = result.filter(item => item.is_vegetarian);
    }

    // Apply spicy filter
    if (filters.spicy) {
      result = result.filter(item => item.is_spicy);
    }

    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        item => {
          const name = language === 'tr' ? item.name : (item.name_en || item.name);
          const description = language === 'tr' ? item.description : (item.description_en || item.description);
          return name.toLowerCase().includes(query) ||
                 (description && description.toLowerCase().includes(query));
        }
      );
    }

    setFilteredItems(result);
  }, [menuItems, activeType, activeCategory, filters, searchQuery]);

  // If no items from database, use default items
  const defaultItems: MenuItem[] = [
    {
      id: '1',
      name: 'Classic Cheeseburger',
      description: 'Juicy beef patty with melted cheddar, lettuce, tomato, and special sauce',
      price: 12.99,
      category: 'Burgers',
      menu_type: 'restaurant',
      image_url: 'https://images.pexels.com/photos/1633578/pexels-photo-1633578.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      is_vegetarian: false,
      is_spicy: false,
      is_available: true,
      created_at: ''
    },
    {
      id: '2',
      name: 'Truffle Fries',
      description: 'Crispy fries tossed with truffle oil, parmesan cheese, and fresh herbs',
      price: 8.99,
      category: 'Sides',
      menu_type: 'restaurant',
      image_url: 'https://images.pexels.com/photos/1893555/pexels-photo-1893555.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      is_vegetarian: true,
      is_spicy: false,
      is_available: true,
      created_at: ''
    },
    {
      id: '3',
      name: 'BBQ Bacon Burger',
      description: 'Smoky beef patty with crispy bacon, cheddar, and tangy BBQ sauce',
      price: 14.99,
      category: 'Burgers',
      menu_type: 'restaurant',
      image_url: 'https://images.pexels.com/photos/3219547/pexels-photo-3219547.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      is_vegetarian: false,
      is_spicy: true,
      is_available: true,
      created_at: ''
    },
    {
      id: '4',
      name: 'Oreo Milkshake',
      description: 'Creamy vanilla milkshake blended with Oreo cookies and topped with whipped cream',
      price: 6.99,
      category: 'Drinks',
      menu_type: 'cafe',
      image_url: 'https://images.pexels.com/photos/3727250/pexels-photo-3727250.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      is_vegetarian: true,
      is_spicy: false,
      is_available: true,
      created_at: ''
    },
    {
      id: '5',
      name: 'Veggie Burger',
      description: 'House-made plant-based patty with avocado, lettuce, tomato, and vegan aioli',
      price: 13.99,
      category: 'Burgers',
      menu_type: 'restaurant',
      image_url: 'https://images.pexels.com/photos/3607284/pexels-photo-3607284.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      is_vegetarian: true,
      is_spicy: false,
      is_available: true,
      created_at: ''
    },
    {
      id: '6',
      name: 'Iced Coffee',
      description: 'Smooth cold brew coffee served over ice with your choice of milk',
      price: 4.99,
      category: 'Drinks',
      menu_type: 'cafe',
      image_url: 'https://images.pexels.com/photos/2638019/pexels-photo-2638019.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      is_vegetarian: true,
      is_spicy: false,
      is_available: true,
      created_at: ''
    }
  ];

  // If no items from database, use default items
  useEffect(() => {
    if (!loading && menuItems.length === 0) {
      setMenuItems(defaultItems);

      // Extract unique categories
      const uniqueCategories = ['All', ...new Set(defaultItems.map(item => item.category))];
      setCategories(uniqueCategories);
    }
  }, [loading, menuItems.length]);

  return (
    <div className="pt-16">
      <SEO
        title={`${t('menu.title')} - Shebo's Burger`}
        description={t('menu.title')}
        keywords={`menu, ${activeType}, burger, coffee, Shebo's Burger`}
        image={activeType === 'restaurant'
          ? 'https://images.pexels.com/photos/1600711/pexels-photo-1600711.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
          : 'https://images.pexels.com/photos/302899/pexels-photo-302899.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
        }
      />

      {/* Menu Header */}
      <section
        className="relative py-24 bg-cover bg-center"
        style={{
          backgroundImage: `linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)), url(${
            activeType === 'restaurant'
              ? 'https://images.pexels.com/photos/1600711/pexels-photo-1600711.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
              : 'https://images.pexels.com/photos/302899/pexels-photo-302899.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
          })`
        }}
      >
        <div className="container-custom text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-5xl font-serif font-bold mb-4 text-white">
              {t('menu.title')}
            </h1>
            <p className="text-xl max-w-2xl mx-auto">
              {activeType === 'restaurant'
                ? t('menu.restaurant')
                : t('menu.cafe')
              }
            </p>
          </motion.div>
        </div>
      </section>

      {/* Menu Toggle and Search */}
      <section className="py-8 bg-cream">
        <div className="container-custom">
          <div className="flex flex-col md:flex-row justify-between items-center gap-6">
            {/* Menu Type Toggle */}
            <div className="flex bg-white rounded-lg shadow-md p-1">
              <button
                className={`px-6 py-2 rounded-md text-lg font-medium transition-colors ${
                  activeType === 'restaurant'
                    ? 'bg-coffee text-white'
                    : 'text-coffee hover:bg-gray-100'
                }`}
                onClick={() => setActiveType('restaurant')}
              >
                {t('menu.restaurant')}
              </button>
              <button
                className={`px-6 py-2 rounded-md text-lg font-medium transition-colors ${
                  activeType === 'cafe'
                    ? 'bg-coffee text-white'
                    : 'text-coffee hover:bg-gray-100'
                }`}
                onClick={() => setActiveType('cafe')}
              >
                {t('menu.cafe')}
              </button>
            </div>

            {/* Search and Filter */}
            <div className="flex items-center gap-3 w-full md:w-auto">
              <div className="relative flex-grow">
                <input
                  type="text"
                  placeholder={t('menu.search')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-coffee"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              </div>

              <button
                className={`p-2 rounded-md ${
                  showFilters ? 'bg-coffee text-white' : 'bg-white text-coffee border border-gray-300'
                }`}
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Filters */}
          {showFilters && (
            <motion.div
              className="mt-4 p-4 bg-white rounded-lg shadow-md"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex flex-wrap gap-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="vegetarian"
                    checked={filters.vegetarian}
                    onChange={() =>
                      setFilters({...filters, vegetarian: !filters.vegetarian})
                    }
                    className="h-4 w-4 text-coffee focus:ring-coffee rounded"
                  />
                  <label htmlFor="vegetarian" className="ml-2 text-coffee">
                    {t('menu.filter.vegetarian')}
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="spicy"
                    checked={filters.spicy}
                    onChange={() =>
                      setFilters({...filters, spicy: !filters.spicy})
                    }
                    className="h-4 w-4 text-coffee focus:ring-coffee rounded"
                  />
                  <label htmlFor="spicy" className="ml-2 text-coffee">
                    {t('menu.filter.spicy')}
                  </label>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </section>

      {/* Categories and Menu Items */}
      <section className="py-12">
        <div className="container-custom">
          {/* Categories */}
          <div className="mb-8 overflow-x-auto">
            <div className="flex space-x-2 min-w-max">
              {categories.map((category) => (
                <button
                  key={category}
                  className={`px-6 py-2 rounded-md text-lg font-medium transition-colors ${
                    activeCategory === category
                      ? 'bg-coffee text-white'
                      : 'bg-white text-coffee border border-coffee hover:bg-coffee/10'
                  }`}
                  onClick={() => setActiveCategory(category)}
                >
                  {category === 'All' ? t('menu.filter.all') : t(`category.${category}`) || category}
                </button>
              ))}
            </div>
          </div>

          {/* Menu Items */}
          {loading ? (
            <div className="text-center py-12">
              <p className="text-xl">{t('common.loading')}</p>
            </div>
          ) : (
            <>
              {filteredItems.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {filteredItems.map((item) => (
                    <MenuItemCard key={item.id} item={item} language={language} t={t} />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-xl">{t('common.error')}</p>
                </div>
              )}
            </>
          )}
        </div>
      </section>
    </div>
  );
};

// Menu Item Component
const MenuItemCard = ({
  item,
  language,
  t
}: {
  item: MenuItem;
  language: string;
  t: (key: string) => string;
}) => {
  const name = language === 'tr' ? item.name : (item.name_en || item.name);
  const description = language === 'tr' ? item.description : (item.description_en || item.description);

  const renderPrice = () => {
    if (item.has_sizes && (item.price_small || item.price_medium || item.price_large)) {
      return (
        <div className="text-right">
          {item.price_small && (
            <div className="text-sm text-coffee">
              {t('common.small')}: {t('menu.currency')}{item.price_small}
            </div>
          )}
          {item.price_medium && (
            <div className="text-sm text-coffee">
              {t('common.medium')}: {t('menu.currency')}{item.price_medium}
            </div>
          )}
          {item.price_large && (
            <div className="text-sm text-coffee">
              {t('common.large')}: {t('menu.currency')}{item.price_large}
            </div>
          )}
        </div>
      );
    } else if (item.price) {
      return (
        <span className="text-lg font-bold text-coffee">
          {t('menu.currency')}{item.price}
        </span>
      );
    }
    return null;
  };

  return (
    <motion.div
      className="menu-item group"
      whileHover={{ y: -5 }}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.4 }}
    >
      {item.image_url && (
        <div className="h-48 overflow-hidden">
          <img
            src={item.image_url}
            alt={name}
            className="w-full h-full object-cover transition-transform group-hover:scale-105"
          />
        </div>
      )}

      <div className="p-6">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <h3 className="font-bold text-xl">{name}</h3>
            <div className="flex space-x-2 mt-1">
              {item.is_vegetarian && (
                <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                  {t('menu.filter.vegetarian')}
                </span>
              )}
              {item.is_spicy && (
                <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                  {t('menu.filter.spicy')}
                </span>
              )}
            </div>
          </div>
          <div className="ml-4">
            {renderPrice()}
          </div>
        </div>
        {description && (
          <p className="text-gray-600 mt-3">{description}</p>
        )}
      </div>
    </motion.div>
  );
};

export default Menu;