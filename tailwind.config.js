/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        coffee: {
          DEFAULT: '#3B2619',
          light: '#5A4031',
          dark: '#2A1A0D',
        },
        mocha: {
          DEFAULT: '#5A4031',
          light: '#7A604B',
          dark: '#4A3021',
        },
        cream: {
          DEFAULT: '#F3EFE9',
          light: '#FFF9F3',
          dark: '#E3DFD9',
        },
        outline: {
          DEFAULT: '#2A1A0D',
        },
      },
      fontFamily: {
        sans: ['Raleway', 'sans-serif'],
        serif: ['Playfair Display', 'serif'],
      },
      typography: {
        coffee: {
          css: {
            '--tw-prose-body': '#2A1A0D',
            '--tw-prose-headings': '#3B2619',
            '--tw-prose-lead': '#5A4031',
            '--tw-prose-links': '#3B2619',
            '--tw-prose-bold': '#2A1A0D',
            '--tw-prose-counters': '#5A4031',
            '--tw-prose-bullets': '#5A4031',
            '--tw-prose-hr': '#E3DFD9',
            '--tw-prose-quotes': '#2A1A0D',
            '--tw-prose-quote-borders': '#3B2619',
            '--tw-prose-captions': '#5A4031',
            '--tw-prose-code': '#3B2619',
            '--tw-prose-pre-code': '#F3EFE9',
            '--tw-prose-pre-bg': '#2A1A0D',
            '--tw-prose-th-borders': '#E3DFD9',
            '--tw-prose-td-borders': '#E3DFD9',
          },
        },
      },
    },
  },
  plugins: [
    function ({ addComponents }) {
      addComponents({
        '.prose': {
          maxWidth: '65ch',
          color: '#2A1A0D',
          '[class~="lead"]': {
            color: '#5A4031',
          },
          'a': {
            color: '#3B2619',
            textDecoration: 'underline',
            fontWeight: '500',
          },
          'strong': {
            color: '#2A1A0D',
            fontWeight: '600',
          },
          'ol': {
            listStyleType: 'decimal',
          },
          'ol[type="A"]': {
            listStyleType: 'upper-alpha',
          },
          'ol[type="a"]': {
            listStyleType: 'lower-alpha',
          },
          'ol[type="I"]': {
            listStyleType: 'upper-roman',
          },
          'ol[type="i"]': {
            listStyleType: 'lower-roman',
          },
          'ul': {
            listStyleType: 'disc',
          },
          'li': {
            marginTop: '0.5em',
            marginBottom: '0.5em',
          },
          'blockquote': {
            fontWeight: '500',
            fontStyle: 'italic',
            color: '#2A1A0D',
            borderLeftWidth: '0.25rem',
            borderLeftColor: '#3B2619',
            quotes: '"\\201C""\\201D""\\2018""\\2019"',
          },
          'h1': {
            color: '#3B2619',
            fontWeight: '800',
            fontSize: '1.875em',
            marginTop: '0',
            marginBottom: '0.8888889em',
            lineHeight: '1.1111111',
          },
          'h2': {
            color: '#3B2619',
            fontWeight: '700',
            fontSize: '1.5em',
            marginTop: '2em',
            marginBottom: '1em',
            lineHeight: '1.3333333',
          },
          'h3': {
            color: '#3B2619',
            fontWeight: '600',
            fontSize: '1.25em',
            marginTop: '1.6em',
            marginBottom: '0.6em',
            lineHeight: '1.6',
          },
          'h4': {
            color: '#3B2619',
            fontWeight: '600',
            marginTop: '1.5em',
            marginBottom: '0.5em',
            lineHeight: '1.5',
          },
          'figure figcaption': {
            color: '#5A4031',
            fontSize: '0.875em',
            lineHeight: '1.4285714',
            marginTop: '0.8571429em',
          },
          'code': {
            color: '#3B2619',
            fontWeight: '600',
          },
          'pre': {
            color: '#F3EFE9',
            backgroundColor: '#2A1A0D',
            overflowX: 'auto',
            fontSize: '0.875em',
            lineHeight: '1.7142857',
            marginTop: '1.7142857em',
            marginBottom: '1.7142857em',
            borderRadius: '0.375rem',
            paddingTop: '0.8571429em',
            paddingRight: '1.1428571em',
            paddingBottom: '0.8571429em',
            paddingLeft: '1.1428571em',
          },
          'pre code': {
            backgroundColor: 'transparent',
            borderWidth: '0',
            borderRadius: '0',
            padding: '0',
            fontWeight: '400',
            color: 'inherit',
            fontSize: 'inherit',
            fontFamily: 'inherit',
            lineHeight: 'inherit',
          },
          'table': {
            width: '100%',
            tableLayout: 'auto',
            textAlign: 'left',
            marginTop: '2em',
            marginBottom: '2em',
          },
          'thead': {
            color: '#2A1A0D',
            fontWeight: '600',
            borderBottomWidth: '1px',
            borderBottomColor: '#E3DFD9',
          },
          'tbody tr': {
            borderBottomWidth: '1px',
            borderBottomColor: '#E3DFD9',
          },
          'hr': {
            borderColor: '#E3DFD9',
            marginTop: '3em',
            marginBottom: '3em',
          },
        },
      });
    },
  ],
};