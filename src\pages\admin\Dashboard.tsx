import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { supabase } from '../../lib/supabaseClient';
import AdminLayout from '../../components/admin/AdminLayout';
import { ArrowRight, Utensils, FileText, LayoutDashboard, Users } from 'lucide-react';

const AdminDashboard = () => {
  const [stats, setStats] = useState({
    menuItems: 0,
    blogPosts: 0,
    contentSections: 0,
  });
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchStats = async () => {
      setLoading(true);
      
      try {
        // Get menu items count
        const { count: menuCount, error: menuError } = await supabase
          .from('menu_items')
          .select('*', { count: 'exact', head: true });
        
        if (menuError) throw menuError;
        
        // Get blog posts count
        const { count: blogCount, error: blogError } = await supabase
          .from('blog_posts')
          .select('*', { count: 'exact', head: true });
        
        if (blogError) throw blogError;
        
        // Get content sections count
        const { count: contentCount, error: contentError } = await supabase
          .from('content')
          .select('*', { count: 'exact', head: true });
        
        if (contentError) throw contentError;
        
        setStats({
          menuItems: menuCount || 0,
          blogPosts: blogCount || 0,
          contentSections: contentCount || 0,
        });
      } catch (error) {
        console.error('Error fetching stats:', error);
        setStats({
          menuItems: 6, // Default values
          blogPosts: 3,
          contentSections: 2,
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchStats();
  }, []);

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    navigate('/login');
  };

  return (
    <AdminLayout>
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Admin Dashboard</h1>
        <p className="text-gray-600">Welcome to Shebo's Burger admin panel.</p>
      </div>
      
      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <StatsCard 
          title="Menu Items" 
          value={stats.menuItems} 
          icon={<Utensils className="h-8 w-8" />} 
          color="bg-green-100 text-green-800"
          loading={loading}
        />
        <StatsCard 
          title="Blog Posts" 
          value={stats.blogPosts} 
          icon={<FileText className="h-8 w-8" />} 
          color="bg-blue-100 text-blue-800"
          loading={loading}
        />
        <StatsCard 
          title="Content Sections" 
          value={stats.contentSections} 
          icon={<LayoutDashboard className="h-8 w-8" />} 
          color="bg-purple-100 text-purple-800"
          loading={loading}
        />
      </div>
      
      {/* Quick Actions */}
      <h2 className="text-xl font-bold mb-4">Quick Actions</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <QuickActionCard 
          title="Manage Menu" 
          description="Add, edit, or delete menu items and categories"
          icon={<Utensils className="h-6 w-6" />}
          link="/admin/menu"
          color="bg-coffee text-white"
        />
        <QuickActionCard 
          title="Manage Blog" 
          description="Create and edit blog posts"
          icon={<FileText className="h-6 w-6" />}
          link="/admin/blog"
          color="bg-mocha text-white"
        />
        <QuickActionCard 
          title="Manage Content" 
          description="Update homepage and other content sections"
          icon={<LayoutDashboard className="h-6 w-6" />}
          link="/admin/content"
          color="bg-[#2A1A0D] text-white"
        />
      </div>
      
      {/* Sign Out Button */}
      <div className="mt-12 text-center">
        <button
          onClick={handleSignOut}
          className="btn bg-red-600 text-white hover:bg-red-700"
        >
          Sign Out
        </button>
      </div>
    </AdminLayout>
  );
};

// Stats Card Component
const StatsCard = ({ 
  title, 
  value, 
  icon, 
  color,
  loading
}: { 
  title: string; 
  value: number; 
  icon: React.ReactNode;
  color: string;
  loading: boolean;
}) => (
  <motion.div 
    className="bg-white rounded-lg shadow-md p-6"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.4 }}
  >
    <div className="flex justify-between items-center mb-4">
      <div className={`p-3 rounded-full ${color}`}>
        {icon}
      </div>
      {loading ? (
        <div className="h-10 w-10 bg-gray-200 rounded-md animate-pulse"></div>
      ) : (
        <span className="text-3xl font-bold">{value}</span>
      )}
    </div>
    <h3 className="text-lg font-semibold">{title}</h3>
  </motion.div>
);

// Quick Action Card Component
const QuickActionCard = ({ 
  title, 
  description, 
  icon, 
  link,
  color
}: { 
  title: string; 
  description: string; 
  icon: React.ReactNode;
  link: string;
  color: string;
}) => (
  <motion.div 
    className="bg-white rounded-lg shadow-md overflow-hidden"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.4 }}
    whileHover={{ y: -5 }}
  >
    <div className={`p-4 ${color}`}>
      {icon}
    </div>
    <div className="p-6">
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <p className="text-gray-600 mb-4">{description}</p>
      <Link 
        to={link} 
        className="inline-flex items-center text-coffee hover:underline font-medium"
      >
        Go to {title}
        <ArrowRight className="ml-2 h-4 w-4" />
      </Link>
    </div>
  </motion.div>
);

export default AdminDashboard;