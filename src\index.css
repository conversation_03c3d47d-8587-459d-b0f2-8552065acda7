@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --color-coffee: 26 14 9; /* #3B2619 */
    --color-mocha: 36 25 19; /* #5A4031 */
    --color-cream: 243 239 233; /* #F3EFE9 */
    --color-outline: 17 10 5; /* #2A1A0D */
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans text-outline bg-cream;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-serif font-bold leading-tight text-coffee;
  }
  
  h1 {
    @apply text-4xl md:text-5xl;
  }
  
  h2 {
    @apply text-3xl md:text-4xl;
  }
  
  h3 {
    @apply text-2xl md:text-3xl;
  }
  
  h4 {
    @apply text-xl md:text-2xl;
  }
  
  p {
    @apply leading-relaxed;
  }
}

@layer components {
  .btn {
    @apply inline-block px-6 py-3 rounded-md font-semibold transition-all duration-300 text-center;
  }
  
  .btn-primary {
    @apply bg-coffee text-cream hover:bg-opacity-90;
  }
  
  .btn-secondary {
    @apply bg-mocha text-cream hover:bg-opacity-90;
  }
  
  .btn-outline {
    @apply border-2 border-coffee text-coffee hover:bg-coffee hover:text-cream;
  }
  
  .container-custom {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section-padding {
    @apply py-12 md:py-16 lg:py-20;
  }
  
  .menu-item {
    @apply bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:shadow-lg;
  }
  
  .blog-card {
    @apply bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:-translate-y-1 hover:shadow-lg;
  }
  
  .form-input {
    @apply w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-coffee;
  }
  
  .form-label {
    @apply block text-sm font-medium text-coffee mb-1;
  }
}

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-cream;
}

::-webkit-scrollbar-thumb {
  @apply bg-mocha bg-opacity-60 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-coffee;
}