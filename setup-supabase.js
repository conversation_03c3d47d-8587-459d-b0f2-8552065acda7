import { createClient } from '@supabase/supabase-js';

// Local Supabase configuration
const supabaseUrl = 'http://127.0.0.1:54321';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupSupabase() {
  console.log('Setting up Supabase with test data...');

  try {
    // Create a test admin user
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'admin123',
      email_confirm: true
    });

    if (authError) {
      console.error('Error creating admin user:', authError);
    } else {
      console.log('✅ Admin user created:', authData.user.email);
    }

    // Insert sample menu items
    const menuItems = [
      {
        name: 'Classic Cheeseburger',
        description: 'Juicy beef patty with melted cheddar, lettuce, tomato, and special sauce',
        price: 12.99,
        category: 'Burgers',
        menu_type: 'restaurant',
        image_url: 'https://images.pexels.com/photos/1633578/pexels-photo-1633578.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
        is_vegetarian: false,
        is_spicy: false,
        is_available: true
      },
      {
        name: 'BBQ Bacon Burger',
        description: 'Smoky beef patty with crispy bacon, cheddar, and tangy BBQ sauce',
        price: 14.99,
        category: 'Burgers',
        menu_type: 'restaurant',
        image_url: 'https://images.pexels.com/photos/3219547/pexels-photo-3219547.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
        is_vegetarian: false,
        is_spicy: true,
        is_available: true
      },
      {
        name: 'Iced Coffee',
        description: 'Smooth cold brew coffee served over ice with your choice of milk',
        price: 4.99,
        category: 'Drinks',
        menu_type: 'cafe',
        image_url: 'https://images.pexels.com/photos/2638019/pexels-photo-2638019.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
        is_vegetarian: true,
        is_spicy: false,
        is_available: true
      }
    ];

    const { error: menuError } = await supabase
      .from('menu_items')
      .insert(menuItems);

    if (menuError) {
      console.error('Error inserting menu items:', menuError);
    } else {
      console.log('✅ Sample menu items inserted');
    }

    // Insert sample blog posts
    const blogPosts = [
      {
        title: 'The Secret to Our Juicy Burgers',
        content: '<p>At Shebo\'s Burger, we take pride in serving the juiciest, most flavorful burgers in town...</p>',
        excerpt: 'Discover what makes our burgers stand out from the competition.',
        image_url: 'https://images.pexels.com/photos/3219547/pexels-photo-3219547.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
        author: 'Chef Michael',
        category: 'Cooking Tips',
        published: true
      }
    ];

    const { error: blogError } = await supabase
      .from('blog_posts')
      .insert(blogPosts);

    if (blogError) {
      console.error('Error inserting blog posts:', blogError);
    } else {
      console.log('✅ Sample blog posts inserted');
    }

    console.log('\n🎉 Supabase setup complete!');
    console.log('📧 Admin email: <EMAIL>');
    console.log('🔑 Admin password: admin123');
    console.log('🌐 Local Supabase URL: http://127.0.0.1:54321');

  } catch (error) {
    console.error('Setup failed:', error);
  }
}

setupSupabase();
