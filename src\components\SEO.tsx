import { useEffect } from 'react';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
}

const SEO = ({
  title = "<PERSON><PERSON>'s Burger - Handcrafted Burgers & Fresh Coffee",
  description = "Experience the best handcrafted burgers made with premium ingredients. <PERSON><PERSON>'s Burger offers an unforgettable dining experience with fresh coffee and delicious food.",
  keywords = "burgers, restaurant, coffee, food, dining, handcrafted, fresh ingredients, cafe, local restaurant",
  image = "https://images.pexels.com/photos/1639562/pexels-photo-1639562.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2",
  url = window.location.href,
  type = "website"
}: SEOProps) => {
  useEffect(() => {
    // Update document title
    document.title = title;

    // Update or create meta tags
    const updateMetaTag = (name: string, content: string, property?: boolean) => {
      const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`;
      let element = document.querySelector(selector) as HTMLMetaElement;
      
      if (!element) {
        element = document.createElement('meta');
        if (property) {
          element.setAttribute('property', name);
        } else {
          element.setAttribute('name', name);
        }
        document.head.appendChild(element);
      }
      
      element.setAttribute('content', content);
    };

    // Basic meta tags
    updateMetaTag('description', description);
    updateMetaTag('keywords', keywords);
    updateMetaTag('author', "Shebo's Burger");
    updateMetaTag('robots', 'index, follow');
    updateMetaTag('viewport', 'width=device-width, initial-scale=1.0');

    // Open Graph meta tags
    updateMetaTag('og:title', title, true);
    updateMetaTag('og:description', description, true);
    updateMetaTag('og:image', image, true);
    updateMetaTag('og:url', url, true);
    updateMetaTag('og:type', type, true);
    updateMetaTag('og:site_name', "Shebo's Burger", true);

    // Twitter Card meta tags
    updateMetaTag('twitter:card', 'summary_large_image');
    updateMetaTag('twitter:title', title);
    updateMetaTag('twitter:description', description);
    updateMetaTag('twitter:image', image);
    updateMetaTag('twitter:site', '@shebosburger');

    // Additional SEO meta tags
    updateMetaTag('theme-color', '#3B2619');
    updateMetaTag('msapplication-TileColor', '#3B2619');

    // Canonical URL
    let canonicalLink = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
    if (!canonicalLink) {
      canonicalLink = document.createElement('link');
      canonicalLink.setAttribute('rel', 'canonical');
      document.head.appendChild(canonicalLink);
    }
    canonicalLink.setAttribute('href', url);

    // JSON-LD structured data
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "Restaurant",
      "name": "Shebo's Burger",
      "description": description,
      "image": image,
      "url": url,
      "telephone": "(*************",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "123 Burger Street",
        "addressLocality": "Foodville",
        "addressRegion": "CA",
        "postalCode": "90210",
        "addressCountry": "US"
      },
      "openingHours": [
        "Mo-Fr 08:00-22:00",
        "Sa 09:00-23:00",
        "Su 09:00-21:00"
      ],
      "servesCuisine": "American",
      "priceRange": "$$",
      "acceptsReservations": false,
      "menu": `${url}/menu`,
      "sameAs": [
        "https://facebook.com/shebosburger",
        "https://instagram.com/shebosburger",
        "https://twitter.com/shebosburger"
      ]
    };

    // Update or create JSON-LD script
    let jsonLdScript = document.querySelector('script[type="application/ld+json"]');
    if (!jsonLdScript) {
      jsonLdScript = document.createElement('script');
      jsonLdScript.setAttribute('type', 'application/ld+json');
      document.head.appendChild(jsonLdScript);
    }
    jsonLdScript.textContent = JSON.stringify(structuredData);

  }, [title, description, keywords, image, url, type]);

  return null; // This component doesn't render anything
};

export default SEO;
