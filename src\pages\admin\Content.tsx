import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import { Edit, X, Save, LayoutDashboard } from 'lucide-react';
import { supabase, ContentSection } from '../../lib/supabaseClient';
import AdminLayout from '../../components/admin/AdminLayout';

type FormData = Omit<ContentSection, 'id'>;

const AdminContent = () => {
  const [contentSections, setContentSections] = useState<ContentSection[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeSection, setActiveSection] = useState<ContentSection | null>(null);
  const [showForm, setShowForm] = useState(false);
  
  const { register, handleSubmit, reset, setValue, formState: { errors } } = useForm<FormData>();

  useEffect(() => {
    fetchContentSections();
  }, []);

  // Fetch content sections
  const fetchContentSections = async () => {
    setLoading(true);
    
    try {
      const { data, error } = await supabase
        .from('content')
        .select('*')
        .order('page')
        .order('order');
      
      if (error) throw error;
      
      if (data) {
        setContentSections(data);
      }
    } catch (error) {
      console.error('Error fetching content sections:', error);
      
      // Use default data if database fetch fails
      const defaultSections: ContentSection[] = [
        {
          id: '1',
          name: 'hero',
          title: 'Indulge in Burger Perfection',
          content: 'Handcrafted burgers made with premium ingredients for an unforgettable taste experience.',
          image_url: 'https://images.pexels.com/photos/1639562/pexels-photo-1639562.jpeg',
          order: 1,
          section: 'hero',
          page: 'home'
        },
        {
          id: '2',
          name: 'about',
          title: 'Our Story',
          content: "Shebo's Burger was founded in 2015 with a simple mission: create the most delicious, high-quality burgers using only the freshest ingredients. What started as a small food truck has grown into a beloved local restaurant, but our commitment to quality and flavor has never wavered.",
          image_url: 'https://images.pexels.com/photos/2983101/pexels-photo-2983101.jpeg',
          order: 2,
          section: 'about',
          page: 'home'
        }
      ];
      
      setContentSections(defaultSections);
    } finally {
      setLoading(false);
    }
  };

  // Handle edit form submission
  const onSubmit = async (data: FormData) => {
    try {
      if (activeSection) {
        // Update existing section
        const { error } = await supabase
          .from('content')
          .update(data)
          .eq('id', activeSection.id);
        
        if (error) throw error;
        
        // Update local state
        setContentSections(prev => 
          prev.map(section => 
            section.id === activeSection.id 
              ? { ...section, ...data } 
              : section
          )
        );
      }
      
      // Reset form and close
      closeForm();
    } catch (error) {
      console.error('Error saving content section:', error);
      alert('Failed to save content section. Please try again.');
    }
  };

  // Open form for editing
  const handleEdit = (section: ContentSection) => {
    setActiveSection(section);
    
    // Set form values
    setValue('name', section.name);
    setValue('title', section.title);
    setValue('content', section.content);
    setValue('image_url', section.image_url || '');
    setValue('order', section.order);
    setValue('section', section.section);
    setValue('page', section.page);
    
    setShowForm(true);
  };

  // Close form
  const closeForm = () => {
    setShowForm(false);
    setActiveSection(null);
    reset();
  };

  // Group content sections by page
  const groupedSections: Record<string, ContentSection[]> = contentSections.reduce((acc, section) => {
    if (!acc[section.page]) {
      acc[section.page] = [];
    }
    acc[section.page].push(section);
    return acc;
  }, {} as Record<string, ContentSection[]>);

  return (
    <AdminLayout>
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Content Management</h1>
        <p className="text-gray-600">Edit website content sections</p>
      </div>
      
      {loading ? (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <p>Loading content sections...</p>
        </div>
      ) : Object.keys(groupedSections).length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <p>No content sections found.</p>
        </div>
      ) : (
        Object.entries(groupedSections).map(([page, sections]) => (
          <div key={page} className="mb-8">
            <h2 className="text-2xl font-bold mb-4 capitalize">{page} Page</h2>
            
            <div className="grid grid-cols-1 gap-6">
              {sections.map((section) => (
                <ContentCard 
                  key={section.id} 
                  section={section} 
                  onEdit={handleEdit} 
                />
              ))}
            </div>
          </div>
        ))
      )}
      
      {/* Edit Form Modal */}
      {showForm && activeSection && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div 
            className="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] overflow-y-auto"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex justify-between items-center p-6 border-b">
              <h2 className="text-2xl font-bold">
                Edit Content Section: {activeSection.name}
              </h2>
              <button 
                onClick={closeForm}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            
            <form onSubmit={handleSubmit(onSubmit)} className="p-6">
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label className="form-label">Title</label>
                  <input
                    {...register('title', { required: 'Title is required' })}
                    className="form-input"
                    placeholder="Section title"
                  />
                  {errors.title && (
                    <p className="text-red-600 text-sm mt-1">{errors.title.message}</p>
                  )}
                </div>
                
                <div>
                  <label className="form-label">Content</label>
                  <textarea
                    {...register('content', { required: 'Content is required' })}
                    className="form-input h-40"
                    placeholder="Section content"
                  />
                  {errors.content && (
                    <p className="text-red-600 text-sm mt-1">{errors.content.message}</p>
                  )}
                </div>
                
                <div>
                  <label className="form-label">Image URL</label>
                  <input
                    {...register('image_url')}
                    className="form-input"
                    placeholder="https://example.com/image.jpg"
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="form-label">Section ID</label>
                    <input
                      {...register('section', { required: 'Section ID is required' })}
                      className="form-input"
                      placeholder="e.g., hero, about"
                      readOnly
                    />
                  </div>
                  
                  <div>
                    <label className="form-label">Page</label>
                    <input
                      {...register('page', { required: 'Page is required' })}
                      className="form-input"
                      placeholder="e.g., home, about"
                      readOnly
                    />
                  </div>
                  
                  <div>
                    <label className="form-label">Order</label>
                    <input
                      type="number"
                      {...register('order', { 
                        required: 'Order is required',
                        min: { value: 1, message: 'Order must be at least 1' }
                      })}
                      className="form-input"
                      placeholder="1"
                    />
                    {errors.order && (
                      <p className="text-red-600 text-sm mt-1">{errors.order.message}</p>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="mt-6 flex justify-end gap-3">
                <button
                  type="button"
                  onClick={closeForm}
                  className="btn bg-gray-200 text-gray-800 hover:bg-gray-300"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn btn-primary flex items-center"
                >
                  <Save className="h-5 w-5 mr-2" />
                  Save Changes
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AdminLayout>
  );
};

// Content Card Component
const ContentCard = ({ 
  section, 
  onEdit 
}: { 
  section: ContentSection; 
  onEdit: (section: ContentSection) => void;
}) => (
  <motion.div 
    className="bg-white rounded-lg shadow-md overflow-hidden"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.4 }}
  >
    <div className="p-4 bg-coffee text-white flex justify-between items-center">
      <div className="flex items-center">
        <LayoutDashboard className="h-5 w-5 mr-2" />
        <span className="font-semibold capitalize">{section.name}</span>
      </div>
      <span className="text-sm bg-white text-coffee px-2 py-1 rounded-full">
        Order: {section.order}
      </span>
    </div>
    
    <div className="p-6">
      <h3 className="text-xl font-bold mb-2">{section.title}</h3>
      <p className="text-gray-600 mb-4">{section.content}</p>
      
      {section.image_url && (
        <div className="mb-4">
          <p className="text-sm text-gray-500 mb-2">Current Image:</p>
          <img 
            src={section.image_url} 
            alt={section.title} 
            className="h-40 w-full object-cover rounded-md"
          />
        </div>
      )}
      
      <div className="flex justify-end">
        <button
          onClick={() => onEdit(section)}
          className="btn bg-coffee text-white hover:bg-coffee/90 flex items-center"
        >
          <Edit className="h-5 w-5 mr-2" />
          Edit Content
        </button>
      </div>
    </div>
  </motion.div>
);

export default AdminContent;