import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, MergeIcon as BurgerIcon } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { motion } from 'framer-motion';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const { session } = useAuth();
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    setIsOpen(false);
  }, [location]);

  return (
    <nav 
      className={`fixed w-full z-50 transition-all duration-300 ${
        scrolled ? 'bg-coffee text-cream py-2 shadow-md' : 'bg-transparent text-coffee py-4'
      }`}
    >
      <div className="container-custom flex justify-between items-center">
        <Link to="/" className="flex items-center">
          <BurgerIcon className="h-8 w-8 mr-2" />
          <span className="font-serif font-bold text-2xl">Shebo's Burger</span>
        </Link>

        {/* Desktop Nav */}
        <div className="hidden md:flex items-center space-x-6">
          <NavLink to="/" label="Home" scrolled={scrolled} />
          <NavLink to="/menu" label="Menu" scrolled={scrolled} />
          <NavLink to="/blog" label="Blog" scrolled={scrolled} />
          
          {session ? (
            <Link 
              to="/admin" 
              className={`btn ${scrolled ? 'btn-outline border-cream text-cream' : 'btn-outline'} py-2 px-4`}
            >
              Admin
            </Link>
          ) : (
            <Link 
              to="/login" 
              className={`btn ${scrolled ? 'btn-outline border-cream text-cream' : 'btn-outline'} py-2 px-4`}
            >
              Login
            </Link>
          )}
        </div>

        {/* Mobile Menu Button */}
        <button 
          className="md:hidden focus:outline-none" 
          onClick={() => setIsOpen(!isOpen)}
        >
          {isOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <Menu className="h-6 w-6" />
          )}
        </button>
      </div>

      {/* Mobile Menu */}
      {isOpen && (
        <motion.div 
          className="md:hidden bg-coffee text-cream"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="container-custom py-4 flex flex-col space-y-3">
            <MobileNavLink to="/" label="Home" />
            <MobileNavLink to="/menu" label="Menu" />
            <MobileNavLink to="/blog" label="Blog" />
            
            {session ? (
              <MobileNavLink to="/admin\" label="Admin Dashboard" />
            ) : (
              <MobileNavLink to="/login\" label="Login" />
            )}
          </div>
        </motion.div>
      )}
    </nav>
  );
};

// NavLink Component for Desktop
const NavLink = ({ to, label, scrolled }: { to: string; label: string; scrolled: boolean }) => {
  const location = useLocation();
  const isActive = location.pathname === to;
  
  return (
    <Link 
      to={to} 
      className={`relative font-medium transition-colors ${
        scrolled 
          ? 'text-cream hover:text-cream/80' 
          : 'text-coffee hover:text-coffee/80'
      } ${isActive ? 'font-bold' : ''}`}
    >
      {label}
      {isActive && (
        <span className={`absolute -bottom-1 left-0 w-full h-0.5 ${
          scrolled ? 'bg-cream' : 'bg-coffee'
        }`} />
      )}
    </Link>
  );
};

// Mobile NavLink Component
const MobileNavLink = ({ to, label }: { to: string; label: string }) => {
  const location = useLocation();
  const isActive = location.pathname === to;
  
  return (
    <Link 
      to={to} 
      className={`py-2 px-4 block rounded-md transition-colors ${
        isActive 
          ? 'bg-mocha font-bold' 
          : 'hover:bg-mocha/50'
      }`}
    >
      {label}
    </Link>
  );
};

export default Navbar;