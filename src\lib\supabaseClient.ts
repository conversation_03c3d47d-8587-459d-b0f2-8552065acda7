import { createClient } from '@supabase/supabase-js';

// These would normally come from environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://your-supabase-url.supabase.co';
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-supabase-anon-key';

export const supabase = createClient(supabaseUrl, supabaseKey);

// Types for database tables
export type MenuItem = {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  menu_type: 'cafe' | 'restaurant';
  image_url?: string;
  is_vegetarian?: boolean;
  is_spicy?: boolean;
  is_available: boolean;
  created_at: string;
};

export type BlogPost = {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  image_url?: string;
  author: string;
  category: string;
  published: boolean;
  created_at: string;
};

export type ContentSection = {
  id: string;
  name: string;
  title: string;
  content: string;
  image_url?: string;
  order: number;
  section: string;
  page: string;
};