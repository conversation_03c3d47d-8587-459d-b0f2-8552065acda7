import { useState, useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { Session } from '@supabase/supabase-js';
import { supabase } from './lib/supabaseClient';

// Components
import Navbar from './components/layout/Navbar';
import Footer from './components/layout/Footer';

// Pages
import Home from './pages/Home';
import Menu from './pages/Menu';
import Blog from './pages/Blog';
import BlogPost from './pages/BlogPost';
import Login from './pages/Login';
import NotFound from './pages/NotFound';

// Admin Pages
import AdminDashboard from './pages/admin/Dashboard';
import AdminMenu from './pages/admin/Menu';
import AdminBlog from './pages/admin/Blog';
import AdminContent from './pages/admin/Content';

// Context
import { AuthProvider } from './context/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';

function App() {
  const [session, setSession] = useState<Session | null>(null);

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });

    return () => subscription.unsubscribe();
  }, []);

  return (
    <LanguageProvider>
      <AuthProvider value={{ session }}>
        <div className="flex flex-col min-h-screen bg-cream">
          <Navbar />
          <main className="flex-grow">
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/menu" element={<Menu />} />
              <Route path="/blog" element={<Blog />} />
              <Route path="/blog/:id" element={<BlogPost />} />
              <Route path="/login" element={<Login />} />

              {/* Admin Routes - Protected */}
              <Route
                path="/admin"
                element={
                  session ? <AdminDashboard /> : <Login />
                }
              />
              <Route
                path="/admin/menu"
                element={
                  session ? <AdminMenu /> : <Login />
                }
              />
              <Route
                path="/admin/blog"
                element={
                  session ? <AdminBlog /> : <Login />
                }
              />
              <Route
                path="/admin/content"
                element={
                  session ? <AdminContent /> : <Login />
                }
              />

              {/* 404 Route */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </main>
          <Footer />
        </div>
      </AuthProvider>
    </LanguageProvider>
  );
}

export default App;