import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import { Plus, Edit, Trash2, X, Search, Calendar, Eye, EyeOff } from 'lucide-react';
import { format } from 'date-fns';
import { supabase, BlogPost } from '../../lib/supabaseClient';
import AdminLayout from '../../components/admin/AdminLayout';

type FormData = Omit<BlogPost, 'id' | 'created_at'>;

const AdminBlog = () => {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [currentPost, setCurrentPost] = useState<BlogPost | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [categories, setCategories] = useState<string[]>([]);
  const [showForm, setShowForm] = useState(false);
  
  const { register, handleSubmit, reset, setValue, formState: { errors } } = useForm<FormData>();

  useEffect(() => {
    fetchBlogPosts();
  }, []);

  // Fetch blog posts
  const fetchBlogPosts = async () => {
    setLoading(true);
    
    try {
      const { data, error } = await supabase
        .from('blog_posts')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      if (data) {
        setPosts(data);
        
        // Extract unique categories
        const uniqueCategories = [...new Set(data.map(post => post.category))];
        setCategories(uniqueCategories);
      }
    } catch (error) {
      console.error('Error fetching blog posts:', error);
      
      // Use default data if database fetch fails
      const defaultPosts: BlogPost[] = [
        {
          id: '1',
          title: 'The Secret to Our Juicy Burgers',
          content: `<p>At Shebo's Burger, we take pride in serving the juiciest, most flavorful burgers in town. But what's our secret? It all starts with quality ingredients.</p><p>We use only premium beef that's ground fresh daily. Our special blend of chuck and brisket provides the perfect fat content for a juicy, flavorful patty. We season our patties with a proprietary mix of spices that enhances the natural flavor of the beef without overwhelming it.</p><p>Another key factor is how we cook our burgers. We use a flat-top grill that sears the patties perfectly, locking in juices and creating a delicious crust. We always cook to order, ensuring each burger reaches your table at peak deliciousness.</p><p>Finally, we pay attention to the details. Our buns are baked fresh daily, and we toast them lightly for the perfect texture. Our toppings are always fresh and high-quality, from crisp lettuce to vine-ripened tomatoes.</p><p>Come taste the difference at Shebo's Burger!</p>`,
          excerpt: 'Discover what makes our burgers stand out from the competition - from ingredient selection to cooking techniques.',
          image_url: 'https://images.pexels.com/photos/3219547/pexels-photo-3219547.jpeg',
          author: 'Chef Michael',
          category: 'Cooking Tips',
          published: true,
          created_at: '2023-06-15T12:00:00Z'
        },
        {
          id: '2',
          title: 'Upcoming Summer Special Menu',
          content: `<p>Summer is just around the corner, and we're excited to announce our new seasonal menu! We've created a selection of fresh, vibrant dishes that capture the essence of summer.</p><p>Our new Tropical Paradise Burger features a juicy beef patty topped with grilled pineapple, crispy bacon, and a zesty mango sauce. It's like a vacation in every bite! For seafood lovers, our Grilled Shrimp Burger with avocado and citrus slaw is a must-try.</p><p>We're also introducing some refreshing new sides. Our Watermelon Feta Salad is the perfect light accompaniment to any burger, and our Sweet Corn Fritters with chili-lime dip are already becoming a customer favorite.</p><p>To help you beat the heat, we've created new summer beverages including a Strawberry Basil Lemonade and a Coconut Cold Brew that are both refreshing and delicious.</p><p>Our summer menu launches on June 1st and will be available through August. Don't miss your chance to try these seasonal specialties!</p>`,
          excerpt: 'Get a sneak peek at our upcoming summer menu featuring seasonal ingredients and tropical flavors.',
          image_url: 'https://images.pexels.com/photos/1600711/pexels-photo-1600711.jpeg',
          author: 'Sarah Williams',
          category: 'Menu Updates',
          published: true,
          created_at: '2023-05-20T14:30:00Z'
        }
      ];
      
      setPosts(defaultPosts);
      
      // Extract unique categories
      const uniqueCategories = [...new Set(defaultPosts.map(post => post.category))];
      setCategories(uniqueCategories);
    } finally {
      setLoading(false);
    }
  };

  // Filter posts when search or posts change
  useEffect(() => {
    let result = posts;
    
    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        post => 
          post.title.toLowerCase().includes(query) || 
          post.content.toLowerCase().includes(query) ||
          post.category.toLowerCase().includes(query) ||
          post.author.toLowerCase().includes(query)
      );
    }
    
    setFilteredPosts(result);
  }, [posts, searchQuery]);

  // Handle add/edit form submission
  const onSubmit = async (data: FormData) => {
    try {
      if (isEditing && currentPost) {
        // Update existing post
        const { error } = await supabase
          .from('blog_posts')
          .update(data)
          .eq('id', currentPost.id);
        
        if (error) throw error;
        
        // Update local state
        setPosts(prev => 
          prev.map(post => 
            post.id === currentPost.id 
              ? { ...post, ...data } 
              : post
          )
        );
      } else {
        // Add new post
        const { data: newPost, error } = await supabase
          .from('blog_posts')
          .insert([{
            ...data,
            created_at: new Date().toISOString()
          }])
          .select();
        
        if (error) throw error;
        
        // Update local state
        if (newPost) {
          setPosts(prev => [...prev, ...newPost]);
          
          // Add new category if it doesn't exist
          if (!categories.includes(data.category)) {
            setCategories(prev => [...prev, data.category]);
          }
        }
      }
      
      // Reset form and close
      closeForm();
    } catch (error) {
      console.error('Error saving blog post:', error);
      alert('Failed to save blog post. Please try again.');
    }
  };

  // Delete blog post
  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this blog post?')) {
      return;
    }
    
    try {
      const { error } = await supabase
        .from('blog_posts')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
      
      // Update local state
      setPosts(prev => prev.filter(post => post.id !== id));
    } catch (error) {
      console.error('Error deleting blog post:', error);
      alert('Failed to delete blog post. Please try again.');
    }
  };

  // Toggle post published status
  const togglePublished = async (post: BlogPost) => {
    try {
      const { error } = await supabase
        .from('blog_posts')
        .update({ published: !post.published })
        .eq('id', post.id);
      
      if (error) throw error;
      
      // Update local state
      setPosts(prev => 
        prev.map(item => 
          item.id === post.id 
            ? { ...item, published: !item.published } 
            : item
        )
      );
    } catch (error) {
      console.error('Error toggling post status:', error);
      alert('Failed to update post status. Please try again.');
    }
  };

  // Open form for editing
  const handleEdit = (post: BlogPost) => {
    setCurrentPost(post);
    setIsEditing(true);
    
    // Set form values
    setValue('title', post.title);
    setValue('content', post.content);
    setValue('excerpt', post.excerpt);
    setValue('category', post.category);
    setValue('author', post.author);
    setValue('image_url', post.image_url || '');
    setValue('published', post.published);
    
    setShowForm(true);
  };

  // Open form for adding
  const handleAdd = () => {
    setCurrentPost(null);
    setIsEditing(false);
    
    // Reset form with defaults
    reset({
      title: '',
      content: '',
      excerpt: '',
      category: '',
      author: '',
      image_url: '',
      published: false,
    });
    
    setShowForm(true);
  };

  // Close form
  const closeForm = () => {
    setShowForm(false);
    setCurrentPost(null);
    setIsEditing(false);
    reset();
  };

  return (
    <AdminLayout>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold mb-2">Blog Management</h1>
          <p className="text-gray-600">Create, edit, and manage blog posts</p>
        </div>
        
        <button
          onClick={handleAdd}
          className="btn bg-coffee text-white hover:bg-coffee/90 flex items-center"
        >
          <Plus className="h-5 w-5 mr-2" />
          New Post
        </button>
      </div>
      
      {/* Search */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <div className="relative">
          <input
            type="text"
            placeholder="Search blog posts..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-coffee"
          />
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
        </div>
      </div>
      
      {/* Blog Posts Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        {loading ? (
          <div className="p-6 text-center">
            <p>Loading blog posts...</p>
          </div>
        ) : filteredPosts.length === 0 ? (
          <div className="p-6 text-center">
            <p>No blog posts found. Try a different search or create a new post.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 text-left">
                <tr>
                  <th className="px-6 py-3 text-coffee font-semibold">Title</th>
                  <th className="px-6 py-3 text-coffee font-semibold">Category</th>
                  <th className="px-6 py-3 text-coffee font-semibold">Author</th>
                  <th className="px-6 py-3 text-coffee font-semibold">Date</th>
                  <th className="px-6 py-3 text-coffee font-semibold">Status</th>
                  <th className="px-6 py-3 text-coffee font-semibold">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredPosts.map((post) => (
                  <tr key={post.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        {post.image_url && (
                          <img 
                            src={post.image_url} 
                            alt={post.title} 
                            className="h-10 w-10 rounded-md object-cover mr-3"
                          />
                        )}
                        <div>
                          <p className="font-medium">{post.title}</p>
                          <p className="text-sm text-gray-500 truncate max-w-xs">
                            {post.excerpt}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">{post.category}</td>
                    <td className="px-6 py-4">{post.author}</td>
                    <td className="px-6 py-4">
                      {format(new Date(post.created_at), 'MMM d, yyyy')}
                    </td>
                    <td className="px-6 py-4">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        post.published 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {post.published ? 'Published' : 'Draft'}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-3">
                        <button
                          onClick={() => togglePublished(post)}
                          className={`${
                            post.published 
                              ? 'text-yellow-600 hover:text-yellow-800' 
                              : 'text-green-600 hover:text-green-800'
                          }`}
                          title={post.published ? 'Unpublish' : 'Publish'}
                        >
                          {post.published ? (
                            <EyeOff className="h-5 w-5" />
                          ) : (
                            <Eye className="h-5 w-5" />
                          )}
                        </button>
                        <button
                          onClick={() => handleEdit(post)}
                          className="text-blue-600 hover:text-blue-800"
                          title="Edit"
                        >
                          <Edit className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => handleDelete(post.id)}
                          className="text-red-600 hover:text-red-800"
                          title="Delete"
                        >
                          <Trash2 className="h-5 w-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      
      {/* Add/Edit Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div 
            className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex justify-between items-center p-6 border-b">
              <h2 className="text-2xl font-bold">
                {isEditing ? 'Edit Blog Post' : 'Create New Blog Post'}
              </h2>
              <button 
                onClick={closeForm}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            
            <form onSubmit={handleSubmit(onSubmit)} className="p-6">
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label className="form-label">Title</label>
                  <input
                    {...register('title', { required: 'Title is required' })}
                    className="form-input"
                    placeholder="Enter post title"
                  />
                  {errors.title && (
                    <p className="text-red-600 text-sm mt-1">{errors.title.message}</p>
                  )}
                </div>
                
                <div>
                  <label className="form-label">Excerpt</label>
                  <textarea
                    {...register('excerpt', { required: 'Excerpt is required' })}
                    className="form-input h-20"
                    placeholder="A brief summary of the post (displayed in previews)"
                  />
                  {errors.excerpt && (
                    <p className="text-red-600 text-sm mt-1">{errors.excerpt.message}</p>
                  )}
                </div>
                
                <div>
                  <label className="form-label">Content (HTML)</label>
                  <textarea
                    {...register('content', { required: 'Content is required' })}
                    className="form-input h-60 font-mono text-sm"
                    placeholder="<p>Your post content in HTML format...</p>"
                  />
                  {errors.content && (
                    <p className="text-red-600 text-sm mt-1">{errors.content.message}</p>
                  )}
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="form-label">Category</label>
                    <input
                      {...register('category', { required: 'Category is required' })}
                      className="form-input"
                      placeholder="e.g., Cooking Tips, News, Events"
                      list="blog-categories"
                    />
                    <datalist id="blog-categories">
                      {categories.map((category) => (
                        <option key={category} value={category} />
                      ))}
                    </datalist>
                    {errors.category && (
                      <p className="text-red-600 text-sm mt-1">{errors.category.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <label className="form-label">Author</label>
                    <input
                      {...register('author', { required: 'Author is required' })}
                      className="form-input"
                      placeholder="Author name"
                    />
                    {errors.author && (
                      <p className="text-red-600 text-sm mt-1">{errors.author.message}</p>
                    )}
                  </div>
                </div>
                
                <div>
                  <label className="form-label">Featured Image URL</label>
                  <input
                    {...register('image_url')}
                    className="form-input"
                    placeholder="https://example.com/image.jpg"
                  />
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="published"
                    {...register('published')}
                    className="h-4 w-4 text-coffee focus:ring-coffee rounded"
                  />
                  <label htmlFor="published" className="ml-2 text-coffee">
                    Publish immediately
                  </label>
                </div>
              </div>
              
              <div className="mt-6 flex justify-end gap-3">
                <button
                  type="button"
                  onClick={closeForm}
                  className="btn bg-gray-200 text-gray-800 hover:bg-gray-300"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn btn-primary"
                >
                  {isEditing ? 'Update Post' : 'Create Post'}
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AdminLayout>
  );
};

export default AdminBlog;